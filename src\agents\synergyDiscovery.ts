import { PipelineContext } from "../pipeline/types";
import { callLLM } from "../utils/llmClient";
import { parseWithRetry } from "../utils/jsonParser";
import { readFileSync } from "fs";
import path from "path";

const systemPrompt = readFileSync(
  path.join(__dirname, "../../prompts/synergyDiscovery.md"),
  "utf-8"
);

export async function runDiscovery(ctx: PipelineContext) {
  const duoNames = ctx.duo.join(" + ");
  const userPrompt = `Characters: ${duoNames}\nTheme: ${ctx.theme || "Default"}`;

  return parseWithRetry(
    () => callLLM(systemPrompt, userPrompt),
    {
      maxRetries: 3,
      retryDelay: 1000,
      fallbackValue: {
        concept: "Generic synergy concept",
        rationale: "Failed to generate specific synergy due to parsing errors",
        motif: "collaboration"
      }
    }
  );
}