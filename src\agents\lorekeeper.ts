import { PipelineContext } from "../pipeline/types";
import { callLLM } from "../utils/llmClient";
import { parseWithRetry } from "../utils/jsonParser";
import { readFileSync } from "fs";
import path from "path";

const systemPrompt = readFileSync(
  path.join(__dirname, "../../prompts/loreKeeper.md"),
  "utf-8"
);

interface LorekeeperResponse {
  status: "APPROVE" | "MODIFY" | "REJECT";
  justification: string;
  adjusted?: {
    concept: string;
    rationale: string;
    motif: string;
  };
}



/**
 * Validates that the response has the expected structure
 */
function validateLorekeeperResponse(data: any): LorekeeperResponse {
  if (!data || typeof data !== 'object') {
    throw new Error('Response is not an object');
  }

  if (!['APPROVE', 'MODIFY', 'REJECT'].includes(data.status)) {
    throw new Error(`Invalid status: ${data.status}. Must be APPROVE, MODIFY, or REJECT`);
  }

  if (!data.justification || typeof data.justification !== 'string') {
    throw new Error('Missing or invalid justification field');
  }

  if (data.status === 'MODIFY') {
    if (!data.adjusted || typeof data.adjusted !== 'object') {
      throw new Error('MODIFY status requires adjusted field');
    }
    if (!data.adjusted.concept || !data.adjusted.rationale || !data.adjusted.motif) {
      throw new Error('adjusted field must contain concept, rationale, and motif');
    }
  }

  return data as LorekeeperResponse;
}

export async function runLorekeeper(ctx: PipelineContext): Promise<LorekeeperResponse> {
  const userPrompt = `Proposal: ${JSON.stringify(ctx.synergyProposal, null, 2)}
Lore Bible: ${readFileSync("data/loreBible.json", "utf-8")}`;

  const fallbackResponse: LorekeeperResponse = {
    status: "REJECT",
    justification: "Failed to process lore validation due to parsing errors"
  };

  return parseWithRetry<LorekeeperResponse>(
    () => callLLM(systemPrompt, userPrompt),
    {
      maxRetries: 3,
      retryDelay: 1000,
      fallbackValue: fallbackResponse,
      validator: validateLorekeeperResponse
    }
  );
}