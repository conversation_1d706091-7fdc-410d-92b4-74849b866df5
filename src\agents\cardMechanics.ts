import { PipelineContext, Card } from "../pipeline/types";
import { callLLM } from "../utils/llmClient";
import { parseWithRetry } from "../utils/jsonParser";
import { readFileSync } from "fs";
import path from "path";

const systemPrompt = readFileSync(
  path.join(__dirname, "../../prompts/cardMechanics.md"),
  "utf-8"
);

export async function runCardMechanics(ctx: PipelineContext): Promise<Partial<Card>> {
  const userPrompt = `Duo: ${ctx.duo.join(" + ")}\nSynergy: ${JSON.stringify(ctx.synergyProposal)}`;

  const fallbackCard: Partial<Card> = {
    name: `${ctx.duo.join(" & ")} Collaboration`,
    type: "Skill",
    cost: 2,
    cooldown: 3,
    character_bond: ctx.duo,
    effects: {
      primary: "Basic collaborative effect",
      split_synergy: {
        used_before_partner: "Prepare for synergy",
        used_after_partner: "Execute synergy"
      }
    },
    synergy_meter_gain: 1,
    rarity: "Common"
  };

  return parseWithRetry<Partial<Card>>(
    () => callLLM(systemPrompt, userPrompt),
    {
      maxRetries: 3,
      retryDelay: 1000,
      fallbackValue: fallbackCard
    }
  );
}