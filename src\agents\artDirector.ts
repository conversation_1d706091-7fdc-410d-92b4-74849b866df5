// src/agents/artDirector.ts
import { Card, ArtBrief } from "../pipeline/types";
import { callLLM } from "../utils/llmClient";
import { parseWithRetry } from "../utils/jsonParser";
import { readFileSync } from "fs";
import path from "path";

const systemPrompt = readFileSync(
  path.join(__dirname, "../../prompts/artDirector.md"),
  "utf-8"
);

/**
 * Validates that the art brief has the expected structure
 */
function validateArtBrief(data: any): ArtBrief {
  if (!data || typeof data !== 'object') {
    throw new Error('Art brief is not an object');
  }

  if (!data.subject || typeof data.subject !== 'string') {
    throw new Error('Missing or invalid subject field');
  }

  if (!data.composition || typeof data.composition !== 'string') {
    throw new Error('Missing or invalid composition field');
  }

  if (!data.palette || typeof data.palette !== 'string') {
    throw new Error('Missing or invalid palette field');
  }

  if (!Array.isArray(data.animation_needs)) {
    throw new Error('animation_needs must be an array');
  }

  if (!Array.isArray(data.fx_overlays)) {
    throw new Error('fx_overlays must be an array');
  }

  return data as ArtBrief;
}

export async function runArtDirector(card: Card): Promise<ArtBrief> {
  const userPrompt = `Card: ${card.name}\nConcept: ${card.concept_image_prompt}\nEffects: ${JSON.stringify(
    card.effects
  )}`;

  const fallbackBrief: ArtBrief = {
    subject: `${card.name} card art`,
    composition: "centered character portrait with dynamic pose",
    palette: "dark fantasy SNES-style palette with muted colors",
    animation_needs: ["idle", "glow"],
    fx_overlays: ["subtle glow", "particle effects"]
  };

  return parseWithRetry<ArtBrief>(
    () => callLLM(systemPrompt, userPrompt),
    {
      maxRetries: 3,
      retryDelay: 1000,
      fallbackValue: fallbackBrief,
      validator: validateArtBrief
    }
  );
}