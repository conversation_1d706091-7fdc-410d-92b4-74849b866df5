import { cleanAndParseJSON, parseWithRetry, parseJSON, JSONParseError } from '../utils/jsonParser';

describe('JSON Parser Utilities', () => {
  describe('cleanAndParseJSON', () => {
    it('should parse valid JSON', () => {
      const validJson = '{"status": "APPROVE", "justification": "Looks good"}';
      const result = cleanAndParseJSON(validJson);
      expect(result).toEqual({ status: "APPROVE", justification: "Looks good" });
    });

    it('should handle JSON wrapped in markdown code blocks', () => {
      const markdownJson = '```json\n{"status": "APPROVE", "justification": "Looks good"}\n```';
      const result = cleanAndParseJSON(markdownJson);
      expect(result).toEqual({ status: "APPROVE", justification: "Looks good" });
    });

    it('should handle JSON wrapped in generic code blocks', () => {
      const codeBlockJson = '```\n{"status": "APPROVE", "justification": "Looks good"}\n```';
      const result = cleanAndParseJSON(codeBlockJson);
      expect(result).toEqual({ status: "APPROVE", justification: "Looks good" });
    });

    it('should extract JSON from text with extra content', () => {
      const textWithJson = 'Here is the response: {"status": "APPROVE", "justification": "Looks good"} - that\'s it!';
      const result = cleanAndParseJSON(textWithJson);
      expect(result).toEqual({ status: "APPROVE", justification: "Looks good" });
    });

    it('should fix trailing commas', () => {
      const jsonWithTrailingComma = '{"status": "APPROVE", "justification": "Looks good",}';
      const result = cleanAndParseJSON(jsonWithTrailingComma);
      expect(result).toEqual({ status: "APPROVE", justification: "Looks good" });
    });

    it('should throw JSONParseError for invalid JSON', () => {
      const invalidJson = '{"status": "APPROVE", "justification": "Looks good"';
      expect(() => cleanAndParseJSON(invalidJson)).toThrow(JSONParseError);
    });

    it('should throw JSONParseError for empty input', () => {
      expect(() => cleanAndParseJSON('')).toThrow(JSONParseError);
      expect(() => cleanAndParseJSON(null as any)).toThrow(JSONParseError);
    });
  });

  describe('parseJSON', () => {
    it('should parse and validate JSON', () => {
      const validJson = '{"status": "APPROVE", "justification": "Looks good"}';
      const validator = (data: any) => {
        if (!data.status || !data.justification) {
          throw new Error('Missing required fields');
        }
        return data;
      };
      
      const result = parseJSON(validJson, validator);
      expect(result).toEqual({ status: "APPROVE", justification: "Looks good" });
    });

    it('should throw validation error', () => {
      const validJson = '{"status": "APPROVE"}';
      const validator = (data: any) => {
        if (!data.justification) {
          throw new Error('Missing justification');
        }
        return data;
      };
      
      expect(() => parseJSON(validJson, validator)).toThrow('Missing justification');
    });
  });

  describe('parseWithRetry', () => {
    it('should succeed on first attempt with valid JSON', async () => {
      const mockLLMCall = jest.fn().mockResolvedValue('{"status": "APPROVE", "justification": "Looks good"}');
      
      const result = await parseWithRetry(mockLLMCall, { maxRetries: 3 });
      
      expect(mockLLMCall).toHaveBeenCalledTimes(1);
      expect(result).toEqual({ status: "APPROVE", justification: "Looks good" });
    });

    it('should retry on JSON parse failure and eventually succeed', async () => {
      const mockLLMCall = jest.fn()
        .mockResolvedValueOnce('invalid json')
        .mockResolvedValueOnce('{"status": "APPROVE", "justification": "Looks good"}');
      
      const result = await parseWithRetry(mockLLMCall, { maxRetries: 3, retryDelay: 0 });
      
      expect(mockLLMCall).toHaveBeenCalledTimes(2);
      expect(result).toEqual({ status: "APPROVE", justification: "Looks good" });
    });

    it('should use fallback value after all retries fail', async () => {
      const mockLLMCall = jest.fn().mockResolvedValue('invalid json');
      const fallback = { status: "REJECT", justification: "Fallback response" };
      
      const result = await parseWithRetry(mockLLMCall, { 
        maxRetries: 2, 
        retryDelay: 0,
        fallbackValue: fallback 
      });
      
      expect(mockLLMCall).toHaveBeenCalledTimes(2);
      expect(result).toEqual(fallback);
    });

    it('should throw error when no fallback is provided and all retries fail', async () => {
      const mockLLMCall = jest.fn().mockResolvedValue('invalid json');
      
      await expect(parseWithRetry(mockLLMCall, { maxRetries: 2, retryDelay: 0 }))
        .rejects.toThrow(JSONParseError);
      
      expect(mockLLMCall).toHaveBeenCalledTimes(2);
    });

    it('should validate responses when validator is provided', async () => {
      const mockLLMCall = jest.fn().mockResolvedValue('{"status": "INVALID"}');
      const validator = (data: any) => {
        if (!['APPROVE', 'MODIFY', 'REJECT'].includes(data.status)) {
          throw new Error('Invalid status');
        }
        return data;
      };
      
      const fallback = { status: "REJECT", justification: "Validation failed" };
      
      const result = await parseWithRetry(mockLLMCall, { 
        maxRetries: 2, 
        retryDelay: 0,
        validator,
        fallbackValue: fallback 
      });
      
      expect(mockLLMCall).toHaveBeenCalledTimes(2);
      expect(result).toEqual(fallback);
    });
  });
});
