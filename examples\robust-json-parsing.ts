/**
 * Example demonstrating robust JSON parsing for LLM responses
 * 
 * This example shows how the improved JSON parsing handles various
 * edge cases that commonly occur with LLM responses.
 */

import { parseWithRetry, parseJSON, JSONParseError } from '../src/utils/jsonParser';

// Simulate different types of LLM responses
const mockLLMResponses = {
  valid: '{"status": "APPROVE", "justification": "This synergy fits the lore perfectly"}',
  
  withMarkdown: '```json\n{"status": "MODIFY", "justification": "Needs adjustment", "adjusted": {"concept": "Updated concept", "rationale": "Better fit", "motif": "harmony"}}\n```',
  
  withExtraText: 'Here is my analysis of the synergy proposal:\n\n{"status": "REJECT", "justification": "Conflicts with established lore"}\n\nI hope this helps!',
  
  withTrailingComma: '{"status": "APPROVE", "justification": "Looks good",}',
  
  malformed: '{"status": "APPROVE", "justification": "Missing closing brace"',
  
  invalidStatus: '{"status": "MAYBE", "justification": "Invalid status value"}',
  
  missingFields: '{"status": "APPROVE"}'
};

// Validator function for lorekeeper responses
function validateLorekeeperResponse(data: any) {
  if (!data || typeof data !== 'object') {
    throw new Error('Response is not an object');
  }
  
  if (!['APPROVE', 'MODIFY', 'REJECT'].includes(data.status)) {
    throw new Error(`Invalid status: ${data.status}. Must be APPROVE, MODIFY, or REJECT`);
  }
  
  if (!data.justification || typeof data.justification !== 'string') {
    throw new Error('Missing or invalid justification field');
  }
  
  if (data.status === 'MODIFY') {
    if (!data.adjusted || typeof data.adjusted !== 'object') {
      throw new Error('MODIFY status requires adjusted field');
    }
    if (!data.adjusted.concept || !data.adjusted.rationale || !data.adjusted.motif) {
      throw new Error('adjusted field must contain concept, rationale, and motif');
    }
  }
  
  return data;
}

async function demonstrateRobustParsing() {
  console.log('=== Robust JSON Parsing Demo ===\n');

  // Example 1: Valid JSON
  console.log('1. Valid JSON:');
  try {
    const result = parseJSON(mockLLMResponses.valid, validateLorekeeperResponse);
    console.log('✅ Success:', result);
  } catch (error) {
    console.log('❌ Error:', (error as Error).message);
  }
  console.log();

  // Example 2: JSON with markdown formatting
  console.log('2. JSON with markdown code blocks:');
  try {
    const result = parseJSON(mockLLMResponses.withMarkdown, validateLorekeeperResponse);
    console.log('✅ Success:', result);
  } catch (error) {
    console.log('❌ Error:', (error as Error).message);
  }
  console.log();

  // Example 3: JSON with extra text
  console.log('3. JSON with surrounding text:');
  try {
    const result = parseJSON(mockLLMResponses.withExtraText, validateLorekeeperResponse);
    console.log('✅ Success:', result);
  } catch (error) {
    console.log('❌ Error:', (error as Error).message);
  }
  console.log();

  // Example 4: JSON with trailing comma (auto-fixed)
  console.log('4. JSON with trailing comma (auto-fixed):');
  try {
    const result = parseJSON(mockLLMResponses.withTrailingComma, validateLorekeeperResponse);
    console.log('✅ Success:', result);
  } catch (error) {
    console.log('❌ Error:', (error as Error).message);
  }
  console.log();

  // Example 5: Malformed JSON
  console.log('5. Malformed JSON:');
  try {
    const result = parseJSON(mockLLMResponses.malformed, validateLorekeeperResponse);
    console.log('✅ Success:', result);
  } catch (error) {
    console.log('❌ Error:', (error as Error).message);
  }
  console.log();

  // Example 6: Invalid status (validation error)
  console.log('6. Invalid status (validation error):');
  try {
    const result = parseJSON(mockLLMResponses.invalidStatus, validateLorekeeperResponse);
    console.log('✅ Success:', result);
  } catch (error) {
    console.log('❌ Error:', (error as Error).message);
  }
  console.log();

  // Example 7: Retry mechanism with fallback
  console.log('7. Retry mechanism with eventual fallback:');
  let attemptCount = 0;
  const mockFailingLLMCall = async () => {
    attemptCount++;
    console.log(`   Attempt ${attemptCount}: Returning malformed JSON`);
    return mockLLMResponses.malformed;
  };

  try {
    const result = await parseWithRetry(mockFailingLLMCall, {
      maxRetries: 3,
      retryDelay: 100, // Short delay for demo
      validator: validateLorekeeperResponse,
      fallbackValue: {
        status: "REJECT",
        justification: "Failed to parse response after multiple attempts"
      }
    });
    console.log('✅ Fallback used:', result);
  } catch (error) {
    console.log('❌ Error:', (error as Error).message);
  }
  console.log();

  // Example 8: Successful retry
  console.log('8. Successful retry after initial failure:');
  let retryAttemptCount = 0;
  const mockRetryLLMCall = async () => {
    retryAttemptCount++;
    if (retryAttemptCount === 1) {
      console.log(`   Attempt ${retryAttemptCount}: Returning malformed JSON`);
      return mockLLMResponses.malformed;
    } else {
      console.log(`   Attempt ${retryAttemptCount}: Returning valid JSON`);
      return mockLLMResponses.valid;
    }
  };

  try {
    const result = await parseWithRetry(mockRetryLLMCall, {
      maxRetries: 3,
      retryDelay: 100,
      validator: validateLorekeeperResponse
    });
    console.log('✅ Success after retry:', result);
  } catch (error) {
    console.log('❌ Error:', (error as Error).message);
  }
}

// Run the demonstration
if (require.main === module) {
  demonstrateRobustParsing().catch(console.error);
}

export { demonstrateRobustParsing };
