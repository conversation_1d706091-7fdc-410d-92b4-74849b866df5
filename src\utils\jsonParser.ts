/**
 * Robust JSON parsing utilities for handling LLM responses
 */

export interface ParseOptions {
  maxRetries?: number;
  retryDelay?: number;
  fallbackValue?: any;
  validator?: (data: any) => any; // Should throw if invalid, return validated data if valid
}

export class JSONParseError extends Error {
  constructor(message: string, public readonly originalError?: Error, public readonly rawText?: string) {
    super(message);
    this.name = 'JSONParseError';
  }
}

/**
 * Attempts to clean and parse JSON from LLM response text
 */
export function cleanAndParseJSON(text: string): any {
  if (!text || typeof text !== 'string') {
    throw new JSONParseError('Input text is empty or not a string');
  }

  let cleanText = text.trim();
  
  // Remove common markdown formatting that LLMs sometimes add
  if (cleanText.startsWith('```json')) {
    cleanText = cleanText.replace(/^```json\s*/, '').replace(/\s*```$/, '');
  } else if (cleanText.startsWith('```')) {
    cleanText = cleanText.replace(/^```\s*/, '').replace(/\s*```$/, '');
  }
  
  // Remove any leading/trailing text that isn't JSON
  const jsonMatch = cleanText.match(/\{[\s\S]*\}/);
  if (jsonMatch) {
    cleanText = jsonMatch[0];
  }
  
  // Try to fix common JSON issues
  cleanText = cleanText
    // Remove trailing commas before closing braces/brackets
    .replace(/,(\s*[}\]])/g, '$1')
    // Fix unescaped quotes in strings (basic attempt)
    .replace(/([^\\])"([^"]*)"([^,}\]]*)/g, '$1"$2"$3');
  
  try {
    return JSON.parse(cleanText);
  } catch (error) {
    throw new JSONParseError(
      `Failed to parse JSON: ${(error as Error).message}`,
      error as Error,
      cleanText
    );
  }
}

/**
 * Robust JSON parsing with retry logic for LLM responses
 */
export async function parseWithRetry<T = any>(
  llmCallFn: () => Promise<string>,
  options: ParseOptions = {}
): Promise<T> {
  const {
    maxRetries = 3,
    retryDelay = 1000,
    fallbackValue,
    validator
  } = options;
  
  let lastError: Error | null = null;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`JSON parse attempt ${attempt}/${maxRetries}`);
      
      const rawResponse = await llmCallFn();
      console.log(`Raw LLM response (attempt ${attempt}):`, rawResponse);
      
      // Try to parse the response
      const parsedData = cleanAndParseJSON(rawResponse);
      
      // Validate if validator is provided
      const validatedData = validator ? validator(parsedData) : parsedData;
      
      console.log(`Successfully parsed and validated response:`, validatedData);
      return validatedData;
      
    } catch (error) {
      lastError = error as Error;
      console.warn(`Parse attempt ${attempt} failed:`, lastError.message);
      
      if (attempt === maxRetries) {
        console.error(`All ${maxRetries} attempts failed. Last error:`, lastError.message);
        break;
      }
      
      // Add delay before retrying
      if (retryDelay > 0) {
        await new Promise(resolve => setTimeout(resolve, retryDelay));
      }
    }
  }
  
  // If all retries failed
  if (fallbackValue !== undefined) {
    console.warn('Using fallback value after all retries failed');
    return fallbackValue;
  }
  
  throw new JSONParseError(
    `Failed to parse JSON after ${maxRetries} attempts. Last error: ${lastError?.message || 'Unknown error'}`,
    lastError || undefined
  );
}

/**
 * Simple wrapper for one-shot JSON parsing with better error messages
 */
export function parseJSON<T = any>(text: string, validator?: (data: any) => T): T {
  try {
    const parsed = cleanAndParseJSON(text);
    return validator ? validator(parsed) : parsed;
  } catch (error) {
    if (error instanceof JSONParseError) {
      throw error;
    }
    throw new JSONParseError(`Unexpected error during JSON parsing: ${(error as Error).message}`, error as Error);
  }
}
