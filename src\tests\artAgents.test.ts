import { runArtDirector } from '../agents/artDirector';
import { runAnimationPlanner } from '../agents/animationPlanner';
import { runFxShader } from '../agents/fxShaderAgent';
import { Card, ArtBrief, AnimationSpec } from '../pipeline/types';
import * as llmClient from '../utils/llmClient';

// Mock the config module
jest.mock('../config', () => ({
  OPENAI_API_KEY: 'mock-api-key'
}));

// Mock the LLM client
jest.mock('../utils/llmClient');
const mockCallLLM = llmClient.callLLM as jest.MockedFunction<typeof llmClient.callLLM>;

// Mock fs for reading prompts
jest.mock('fs', () => ({
  readFileSync: jest.fn().mockReturnValue('Mock system prompt')
}));

describe('Art Pipeline Agents', () => {
  const mockCard: Card = {
    name: "Test Card",
    concept_image_prompt: "Two warriors fighting together",
    type: "Attack",
    cost: 3,
    cooldown: 2,
    character_bond: ["Hero", "Mage"],
    effects: {
      primary: "Deal damage",
      split_synergy: {
        used_before_partner: "Charge up",
        used_after_partner: "Execute combo"
      }
    },
    synergy_meter_gain: 2,
    rarity: "Rare",
    background_lore: "Test lore",
    flavor_text: "Test flavor"
  };

  const mockArtBrief: ArtBrief = {
    subject: "Two warriors in combat stance",
    composition: "dynamic action pose",
    palette: "dark fantasy colors",
    animation_needs: ["attack", "combo"],
    fx_overlays: ["sparks", "energy"]
  };

  const mockAnimationSpec: AnimationSpec = {
    frames: 4,
    size: [128, 128],
    sequence: ["idle", "charge", "attack", "recover"]
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('runArtDirector', () => {
    it('should parse valid art brief JSON', async () => {
      const validResponse = JSON.stringify({
        subject: "Epic battle scene",
        composition: "centered with dynamic movement",
        palette: "dark fantasy SNES palette",
        animation_needs: ["slash", "impact"],
        fx_overlays: ["sword trail", "sparks"]
      });

      mockCallLLM.mockResolvedValue(validResponse);

      const result = await runArtDirector(mockCard);

      expect(result).toEqual({
        subject: "Epic battle scene",
        composition: "centered with dynamic movement",
        palette: "dark fantasy SNES palette",
        animation_needs: ["slash", "impact"],
        fx_overlays: ["sword trail", "sparks"]
      });
    });

    it('should handle malformed JSON with fallback', async () => {
      mockCallLLM.mockResolvedValue('invalid json');

      const result = await runArtDirector(mockCard);

      expect(result).toEqual({
        subject: "Test Card card art",
        composition: "centered character portrait with dynamic pose",
        palette: "dark fantasy SNES-style palette with muted colors",
        animation_needs: ["idle", "glow"],
        fx_overlays: ["subtle glow", "particle effects"]
      });
    });

    it('should handle JSON with missing required fields', async () => {
      const invalidResponse = JSON.stringify({
        subject: "Test subject"
        // Missing other required fields
      });

      mockCallLLM.mockResolvedValue(invalidResponse);

      const result = await runArtDirector(mockCard);

      // Should use fallback due to validation failure
      expect(result.subject).toBe("Test Card card art");
      expect(result.composition).toBeDefined();
      expect(result.palette).toBeDefined();
    });
  });

  describe('runAnimationPlanner', () => {
    it('should parse valid animation spec JSON', async () => {
      const validResponse = JSON.stringify({
        frames: 6,
        size: [256, 256],
        sequence: ["idle", "windup", "strike", "follow", "recover", "return"]
      });

      mockCallLLM.mockResolvedValue(validResponse);

      const result = await runAnimationPlanner(mockArtBrief, mockCard);

      expect(result).toEqual({
        frames: 6,
        size: [256, 256],
        sequence: ["idle", "windup", "strike", "follow", "recover", "return"]
      });
    });

    it('should handle malformed JSON with fallback', async () => {
      mockCallLLM.mockResolvedValue('{"frames": invalid}');

      const result = await runAnimationPlanner(mockArtBrief, mockCard);

      expect(result).toEqual({
        frames: 4,
        size: [128, 128],
        sequence: ["idle", "charge", "impact", "fade"]
      });
    });

    it('should validate frame count is positive', async () => {
      const invalidResponse = JSON.stringify({
        frames: -1,
        size: [128, 128],
        sequence: ["test"]
      });

      mockCallLLM.mockResolvedValue(invalidResponse);

      const result = await runAnimationPlanner(mockArtBrief, mockCard);

      // Should use fallback due to validation failure
      expect(result.frames).toBe(4);
    });
  });

  describe('runFxShader', () => {
    it('should parse valid FX shader package JSON', async () => {
      const validResponse = JSON.stringify({
        fx_overlays: ["lightning", "glow", "particles"],
        shader_code: "uniform float time; void main() { gl_FragColor = vec4(1.0); }"
      });

      mockCallLLM.mockResolvedValue(validResponse);

      const result = await runFxShader(mockArtBrief, mockCard, mockAnimationSpec);

      expect(result).toEqual({
        fx_overlays: ["lightning", "glow", "particles"],
        shader_code: "uniform float time; void main() { gl_FragColor = vec4(1.0); }"
      });
    });

    it('should handle malformed JSON with fallback', async () => {
      mockCallLLM.mockResolvedValue('invalid shader json');

      const result = await runFxShader(mockArtBrief, mockCard, mockAnimationSpec);

      expect(result.fx_overlays).toEqual(["glow", "sparkle"]);
      expect(result.shader_code).toContain("Basic sprite shader");
    });

    it('should validate fx_overlays is an array', async () => {
      const invalidResponse = JSON.stringify({
        fx_overlays: "not an array",
        shader_code: "valid shader code"
      });

      mockCallLLM.mockResolvedValue(invalidResponse);

      const result = await runFxShader(mockArtBrief, mockCard, mockAnimationSpec);

      // Should use fallback due to validation failure
      expect(Array.isArray(result.fx_overlays)).toBe(true);
    });
  });
});
