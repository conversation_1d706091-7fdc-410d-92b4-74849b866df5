# Art Agent Pipeline Documentation

## Overview
Art generation begins only after the **card <PERSON><PERSON><PERSON> is fully finalized by the Proofreader <PERSON>**.
This prevents wasted image API costs on re-generation.

Pipeline:
1. Art Director Agent
2. Concept Art Generator Agent
3. Animation Planner Agent
4. FX/Shader Agent
(Human confirmation is required between each stage.)

---

## 1. Art Director Agent
**Role**: Produce a structured art brief consistent with the game's style.  
**Input**: Final Card JSON (includes concept_image_prompt).  
**Output** (JSON):
```json
{
  "subject": "main focal element",
  "composition": "viewpoint/pose/layout",
  "palette": "palette or tone guide",
  "animation_needs": ["glow intensify", "burst frame"],
  "fx_overlays": ["embers", "smoke wisps"]
}
```

Constraints:
- Must enforce consistent style (pixel art SNES-era palette, dark fantasy).  
- Must remain concise and structured.

---

## 2. Concept Art Generator Agent
**Role**: Convert art brief into base 2D sprite(s).  
**Input**: Art brief JSON.  
**Output**: 
- PNG image(s) 128x128, stored under `/data/generated/art/sprites/`.

Constraints:
- Keep pixel art consistency (SNES/Sega JRPG vibe).
- Generate minimal size to reduce costs, upscale later.

---

## 3. Animation Planner Agent
**Role**: Output a spritesheet layout for the card’s animated form.  
**Input**: Art brief JSON (with effects).  
**Output JSON**:
```json
{
  "frames": 4,
  "size": [128, 128],
  "sequence": ["idle", "charge", "impact", "fade"]
}
```

Constraints:
- Names frames clearly.
- Keeps frames minimal (3-6 recommended for budget).

---

## 4. FX/Shader Agent
**Role**: Generate FX overlays and short shader snippets for impact.  
**Input**: Card JSON + art brief.  
**Output**:
- Overlay textures/PNGs (e.g. glowing layers, burst masks).
- GLSL snippet for effect (per-sprite, not full-screen).

Example GLSL output:

```glsl
// Ember glow shader
void main() {
  vec4 texColor = texture2D(u_texture, v_texCoord);
  float glow = 0.5 + 0.5 * sin(u_time * 5.0);
  gl_FragColor = texColor + vec4(glow*0.2, glow*0.1, 0.0, 0.0);
}
```

Constraints:
- Must remain short, optimized for runtime shaders.
- Avoid full-screen effects.

---

## Human Confirmation
Between each stage, a confirmation prompt is presented to a human designer:

- ✔ Approve → continue.  
- ✖ Reject → halt until adjustments applied.  

This **saves GPU/image costs** and keeps artistic coherence.
