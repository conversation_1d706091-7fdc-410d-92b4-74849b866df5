# Card Agent Pipeline Documentation

## Overview
The card creation pipeline transforms a simple duo of characters into a fully validated,
playable card with lore, effects, and mechanical balance.

Pipeline:
1. Synergy Discovery Agent
2. Lore Keeper Agent
3. Card Mechanics Designer Agent
4. Flavor Agent
5. Proofreader & Consistency Agent

Each agent consumes and outputs structured JSON, ensuring a stable "contract" for downstream steps.

---

## 1. Synergy Discovery Agent
**Role**: Propose emergent thematic synergy for a pair of characters.  
**Input**: Two characters, optional theme.  
**Output** (JSON):
```json
{
  "concept": "short-phrase synergy concept",
  "rationale": "1-2 sentence justification",
  "motif": "symbolic image or idea"
}
```

Constraints:
- Must be concise.
- Must capture both *mechanical* and *lore* resonance.

---

## 2. Lore Keeper Agent
**Role**: Validate synergy proposals against the game's lore bible.  
**Input**: Proposal JSON.  
**Output** (JSON):
```json
{
  "status": "APPROVE" | "MODIFY" | "REJECT",
  "justification": "short string",
  "adjusted": { "concept": "...", "rationale": "...", "motif": "..." } // optional
}
```

Constraints:
- References `loreBible.json`.
- Prevents motifs/elements inconsistent with established tone/world.

---

## 3. Card Mechanics Designer Agent
**Role**: Translate synergy concept into mechanical design.  
**Input**: Duo names + approved synergy.  
**Output** (JSON):
```json
{
  "name": "...",
  "type": "Attack|Skill|Power|Ultimate",
  "cost": int,
  "cooldown": int,
  "character_bond": ["CharA", "CharB"],
  "effects": {
    "primary": "...",
    "split_synergy": {
      "used_before_partner": "...",
      "used_after_partner": "..."
    }
  },
  "synergy_meter_gain": int,
  "rarity": "Common|Rare|Epic|Legendary"
}
```

Constraints:
- Mechanics only. No lore/flavor text.
- Must respect energy curve and cooldown balance.

---

## 4. Flavor Agent
**Role**: Add lore, flavor text, and concept image prompt.  
**Input**: Mechanic-only card JSON.  
**Output** (JSON fragment merged with card):
```json
{
  "background_lore": "...",
  "flavor_text": "...",
  "concept_image_prompt": "short art description"
}
```

Constraints:
- Lore elements must connect to world and bond theme.
- Concept art description ≤ 40 words.

---

## 5. Proofreader & Consistency Agent
**Role**: Validate and finalize card JSON.  
**Input**: Draft card JSON.  
**Output**: Final `Card` JSON schema (all fields filled).  

Tasks:
- Standardize terminology ("Block" not "Armor").  
- Ensure concise and readable rule text.  
- Correct formatting or grammar problems.  
