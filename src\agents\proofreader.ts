import { PipelineContext, Card } from "../pipeline/types";
import { callLLM } from "../utils/llmClient";
import { parseWithRetry } from "../utils/jsonParser";
import { readFileSync } from "fs";
import path from "path";

const systemPrompt = readFileSync(
  path.join(__dirname, "../../prompts/proofreader.md"),
  "utf-8"
);

export async function runProofreader(ctx: PipelineContext): Promise<Card> {
  const userPrompt = `Draft Card:\n${JSON.stringify(ctx.cardDraft, null, 2)}`;

  // Create a fallback card by merging mechanics and cardDraft
  const fallbackCard: Card = {
    name: ctx.mechanics?.name || `${ctx.duo.join(" & ")} Card`,
    concept_image_prompt: ctx.cardDraft?.concept_image_prompt || "Two characters collaborating",
    type: ctx.mechanics?.type || "Skill",
    cost: ctx.mechanics?.cost || 2,
    cooldown: ctx.mechanics?.cooldown || 3,
    character_bond: ctx.duo,
    effects: ctx.mechanics?.effects || {
      primary: "Basic collaborative effect",
      split_synergy: {
        used_before_partner: "Prepare for synergy",
        used_after_partner: "Execute synergy"
      }
    },
    synergy_meter_gain: ctx.mechanics?.synergy_meter_gain || 1,
    rarity: ctx.mechanics?.rarity || "Common",
    background_lore: ctx.cardDraft?.background_lore || "A tale of collaboration.",
    flavor_text: ctx.cardDraft?.flavor_text || "Together, they are stronger."
  };

  return parseWithRetry<Card>(
    () => callLLM(systemPrompt, userPrompt),
    {
      maxRetries: 3,
      retryDelay: 1000,
      fallbackValue: fallbackCard
    }
  );
}