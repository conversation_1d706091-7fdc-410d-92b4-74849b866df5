/**
 * Example demonstrating robust JSON parsing in the art pipeline
 * 
 * This example shows how the art pipeline agents handle various
 * edge cases and malformed responses gracefully.
 */

import { runArtDirector } from '../src/agents/artDirector';
import { runAnimationPlanner } from '../src/agents/animationPlanner';
import { runFxShader } from '../src/agents/fxShaderAgent';
import { Card, ArtBrief, AnimationSpec } from '../src/pipeline/types';

// Mock the LLM client for demonstration
jest.mock('../src/utils/llmClient');
jest.mock('../src/config', () => ({ OPENAI_API_KEY: 'demo-key' }));
jest.mock('fs', () => ({ readFileSync: () => 'Mock prompt' }));

import * as llmClient from '../src/utils/llmClient';
const mockCallLLM = llmClient.callLLM as jest.MockedFunction<typeof llmClient.callLLM>;

const demoCard: Card = {
  name: "Lightning Strike",
  concept_image_prompt: "A warrior and mage combining lightning magic with sword combat",
  type: "Attack",
  cost: 4,
  cooldown: 3,
  character_bond: ["Thunder Warrior", "Storm Mage"],
  effects: {
    primary: "Deal 8 lightning damage to target enemy",
    split_synergy: {
      used_before_partner: "Charge weapon with lightning energy",
      used_after_partner: "Channel lightning through charged weapon for massive damage"
    }
  },
  synergy_meter_gain: 3,
  rarity: "Epic",
  background_lore: "When thunder meets storm, the very air crackles with power.",
  flavor_text: "The blade sang with electric fury as magic and steel became one."
};

async function demonstrateArtPipeline() {
  console.log('=== Robust Art Pipeline Demo ===\n');

  // Scenario 1: Art Director with valid response
  console.log('1. Art Director - Valid Response:');
  mockCallLLM.mockResolvedValueOnce(JSON.stringify({
    subject: "Lightning-charged warrior with mage casting storm magic",
    composition: "Dynamic diagonal composition with warrior in foreground, mage behind",
    palette: "Electric blues and purples with bright yellow lightning accents",
    animation_needs: ["lightning charge", "weapon glow", "spell casting"],
    fx_overlays: ["lightning bolts", "electric sparks", "magical aura"]
  }));

  const artBrief = await runArtDirector(demoCard);
  console.log('✅ Art Brief:', artBrief);
  console.log();

  // Scenario 2: Art Director with malformed JSON (fallback)
  console.log('2. Art Director - Malformed JSON (uses fallback):');
  mockCallLLM.mockResolvedValue('{"subject": "Lightning warrior", "composition": invalid json}');

  const fallbackBrief = await runArtDirector(demoCard);
  console.log('✅ Fallback Brief:', fallbackBrief);
  console.log();

  // Scenario 3: Animation Planner with valid response
  console.log('3. Animation Planner - Valid Response:');
  mockCallLLM.mockResolvedValueOnce(JSON.stringify({
    frames: 8,
    size: [256, 256],
    sequence: ["idle", "charge_start", "charge_build", "lightning_strike", "impact", "discharge", "recovery", "return"]
  }));

  const animSpec = await runAnimationPlanner(artBrief, demoCard);
  console.log('✅ Animation Spec:', animSpec);
  console.log();

  // Scenario 4: Animation Planner with validation error (negative frames)
  console.log('4. Animation Planner - Validation Error (uses fallback):');
  mockCallLLM.mockResolvedValue(JSON.stringify({
    frames: -5,  // Invalid: negative frames
    size: [128, 128],
    sequence: ["broken"]
  }));

  const fallbackAnimSpec = await runAnimationPlanner(artBrief, demoCard);
  console.log('✅ Fallback Animation Spec:', fallbackAnimSpec);
  console.log();

  // Scenario 5: FX Shader with valid response
  console.log('5. FX Shader Agent - Valid Response:');
  mockCallLLM.mockResolvedValueOnce(JSON.stringify({
    fx_overlays: ["lightning_bolt", "electric_aura", "spark_particles", "energy_trail"],
    shader_code: `// Lightning effect shader
uniform float time;
uniform sampler2D mainTexture;
varying vec2 vUv;

void main() {
  vec4 color = texture2D(mainTexture, vUv);
  
  // Lightning flicker effect
  float flicker = sin(time * 15.0) * 0.1 + 0.9;
  
  // Electric blue tint
  vec3 lightning = vec3(0.3, 0.7, 1.0);
  color.rgb = mix(color.rgb, lightning, 0.2);
  
  gl_FragColor = color * flicker;
}`
  }));

  const fxPackage = await runFxShader(artBrief, demoCard, animSpec);
  console.log('✅ FX Package:', fxPackage);
  console.log();

  // Scenario 6: FX Shader with completely invalid JSON
  console.log('6. FX Shader Agent - Invalid JSON (uses fallback):');
  mockCallLLM.mockResolvedValue('This is not JSON at all! The shader failed to generate.');

  const fallbackFxPackage = await runFxShader(artBrief, demoCard, animSpec);
  console.log('✅ Fallback FX Package:', fallbackFxPackage);
  console.log();

  // Scenario 7: Complete pipeline with mixed success/failure
  console.log('7. Complete Pipeline - Mixed Results:');
  
  // Art Director succeeds
  mockCallLLM.mockResolvedValueOnce('```json\n{"subject": "Epic battle", "composition": "centered", "palette": "dark", "animation_needs": ["attack"], "fx_overlays": ["glow"]}\n```');
  const pipelineBrief = await runArtDirector(demoCard);
  
  // Animation Planner fails, uses fallback
  mockCallLLM.mockResolvedValueOnce('malformed animation json');
  const pipelineAnimSpec = await runAnimationPlanner(pipelineBrief, demoCard);
  
  // FX Shader succeeds with markdown formatting
  mockCallLLM.mockResolvedValueOnce(`Here's the shader code:
\`\`\`json
{
  "fx_overlays": ["magic_sparkles", "energy_waves"],
  "shader_code": "uniform float time; void main() { gl_FragColor = vec4(1.0); }"
}
\`\`\`
Hope this helps!`);
  const pipelineFxPackage = await runFxShader(pipelineBrief, demoCard, pipelineAnimSpec);
  
  console.log('Pipeline Results:');
  console.log('- Art Brief Subject:', pipelineBrief.subject);
  console.log('- Animation Frames:', pipelineAnimSpec.frames, '(fallback used)');
  console.log('- FX Overlays:', pipelineFxPackage.fx_overlays);
  console.log();

  console.log('🎉 Art pipeline completed successfully with robust error handling!');
  console.log('Even with malformed responses, the pipeline produces usable results.');
}

// Run the demonstration
if (require.main === module) {
  demonstrateArtPipeline().catch(console.error);
}

export { demonstrateArtPipeline };
