// src/agents/animationPlanner.ts
import { AnimationSpec, ArtBrief, Card } from "../pipeline/types";
import { callLLM } from "../utils/llmClient";
import { parseWithRetry } from "../utils/jsonParser";
import { readFileSync } from "fs";
import path from "path";

const systemPrompt = readFileSync(
  path.join(__dirname, "../../prompts/animationPlanner.md"),
  "utf-8"
);

/**
 * Validates that the animation spec has the expected structure
 */
function validateAnimationSpec(data: any): AnimationSpec {
  if (!data || typeof data !== 'object') {
    throw new Error('Animation spec is not an object');
  }

  if (typeof data.frames !== 'number' || data.frames < 1) {
    throw new Error('frames must be a positive number');
  }

  if (!Array.isArray(data.size) || data.size.length !== 2 ||
      typeof data.size[0] !== 'number' || typeof data.size[1] !== 'number') {
    throw new Error('size must be an array of two numbers [width, height]');
  }

  if (!Array.isArray(data.sequence) || data.sequence.length === 0) {
    throw new Error('sequence must be a non-empty array of frame names');
  }

  // Validate that all sequence items are strings
  if (!data.sequence.every((item: any) => typeof item === 'string')) {
    throw new Error('all sequence items must be strings');
  }

  return data as AnimationSpec;
}

export async function runAnimationPlanner(
  brief: ArtBrief,
  card: Card
): Promise<AnimationSpec> {
  const userPrompt = `Card: ${card.name}\nBrief: ${JSON.stringify(brief)}`;

  const fallbackSpec: AnimationSpec = {
    frames: 4,
    size: [128, 128],
    sequence: ["idle", "charge", "impact", "fade"]
  };

  return parseWithRetry<AnimationSpec>(
    () => callLLM(systemPrompt, userPrompt),
    {
      maxRetries: 3,
      retryDelay: 1000,
      fallbackValue: fallbackSpec,
      validator: validateAnimationSpec
    }
  );
}