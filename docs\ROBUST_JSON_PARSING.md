# Robust JSON Parsing for LLM Responses

## Problem

When calling LLMs with prompts that request JSON output, several issues commonly occur:

1. **Invalid JSON syntax** - Missing brackets, trailing commas, unescaped quotes
2. **Markdown formatting** - LLMs often wrap <PERSON><PERSON><PERSON> in ```json code blocks
3. **Extra text** - LLMs may add explanatory text before/after the JSON
4. **Inconsistent responses** - Sometimes valid, sometimes invalid JSON
5. **No error recovery** - A single malformed response breaks the entire pipeline

## Solution

We've implemented a robust JSON parsing system with the following features:

### 1. Smart JSON Cleaning (`cleanAndParseJSON`)

Automatically handles common LLM response patterns:

```typescript
// Handles markdown code blocks
const response = '```json\n{"status": "APPROVE"}\n```';
const parsed = cleanAndParseJSON(response); // ✅ Works

// Extracts JSON from surrounding text  
const response = 'Here is the result: {"status": "APPROVE"} - done!';
const parsed = cleanAndParseJSON(response); // ✅ Works

// Fixes trailing commas
const response = '{"status": "APPROVE", "justification": "Good",}';
const parsed = cleanAndParseJSON(response); // ✅ Works
```

### 2. Retry Logic with Fallbacks (`parseWithRetry`)

Automatically retries failed parsing attempts with configurable fallbacks:

```typescript
const result = await parseWithRetry(
  () => callLLM(systemPrompt, userPrompt),
  {
    maxRetries: 3,           // Try up to 3 times
    retryDelay: 1000,        // Wait 1s between retries
    fallbackValue: {         // Use this if all retries fail
      status: "REJECT",
      justification: "Parsing failed"
    },
    validator: validateResponse // Optional validation function
  }
);
```

### 3. Response Validation

Ensures parsed JSON matches expected structure:

```typescript
function validateLorekeeperResponse(data: any) {
  if (!['APPROVE', 'MODIFY', 'REJECT'].includes(data.status)) {
    throw new Error(`Invalid status: ${data.status}`);
  }
  
  if (!data.justification) {
    throw new Error('Missing justification field');
  }
  
  // Additional validation logic...
  return data;
}
```

### 4. Comprehensive Error Handling

Custom error types with detailed information:

```typescript
try {
  const result = parseJSON(response, validator);
} catch (error) {
  if (error instanceof JSONParseError) {
    console.log('Parse error:', error.message);
    console.log('Raw text:', error.rawText);
    console.log('Original error:', error.originalError);
  }
}
```

## Implementation

### Updated Agent Pattern

All card and art pipeline agents now use the robust parsing pattern:

```typescript
import { parseWithRetry } from "../utils/jsonParser";

export async function runLorekeeper(ctx: PipelineContext): Promise<LorekeeperResponse> {
  const userPrompt = `Proposal: ${JSON.stringify(ctx.synergyProposal, null, 2)}`;

  const fallbackResponse: LorekeeperResponse = {
    status: "REJECT",
    justification: "Failed to process lore validation due to parsing errors"
  };

  return parseWithRetry<LorekeeperResponse>(
    () => callLLM(systemPrompt, userPrompt),
    {
      maxRetries: 3,
      retryDelay: 1000,
      fallbackValue: fallbackResponse,
      validator: validateLorekeeperResponse
    }
  );
}
```

### Art Pipeline Agents

Art pipeline agents also use the same robust pattern with appropriate fallbacks:

```typescript
// Art Director with fallback art brief
export async function runArtDirector(card: Card): Promise<ArtBrief> {
  const fallbackBrief: ArtBrief = {
    subject: `${card.name} card art`,
    composition: "centered character portrait with dynamic pose",
    palette: "dark fantasy SNES-style palette with muted colors",
    animation_needs: ["idle", "glow"],
    fx_overlays: ["subtle glow", "particle effects"]
  };

  return parseWithRetry<ArtBrief>(
    () => callLLM(systemPrompt, userPrompt),
    {
      maxRetries: 3,
      retryDelay: 1000,
      fallbackValue: fallbackBrief,
      validator: validateArtBrief
    }
  );
}
```

### Benefits

1. **Resilient Pipeline** - Single malformed responses don't break the entire pipeline
2. **Better User Experience** - Fallback responses provide meaningful defaults
3. **Debugging Support** - Detailed error messages help identify issues
4. **Consistent Interface** - All agents use the same robust parsing approach
5. **Configurable Behavior** - Retry counts, delays, and fallbacks can be customized
6. **Cross-Pipeline Consistency** - Both card and art pipelines use the same error handling

## Testing

Run the comprehensive test suites:

```bash
# Test the JSON parser utility
npm test src/tests/jsonParser.test.ts

# Test the card pipeline agents
npm test src/tests/jsonParser.test.ts

# Test the art pipeline agents
npm test src/tests/artAgents.test.ts
```

Try the interactive demonstrations:

```bash
# General JSON parsing demo
npx ts-node examples/robust-json-parsing.ts

# Art pipeline demo
npx ts-node examples/robust-art-pipeline.ts
```

## Migration Guide

### Before (Naive Pattern)
```typescript
export async function runAgent(ctx: PipelineContext) {
  const result = await callLLM(systemPrompt, userPrompt);
  return JSON.parse(result); // ❌ Can fail
}
```

### After (Robust Pattern)
```typescript
import { parseWithRetry } from "../utils/jsonParser";

export async function runAgent(ctx: PipelineContext) {
  return parseWithRetry(
    () => callLLM(systemPrompt, userPrompt),
    {
      maxRetries: 3,
      fallbackValue: { /* sensible defaults */ },
      validator: validateResponse // optional
    }
  );
}
```

## Configuration Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `maxRetries` | number | 3 | Maximum number of retry attempts |
| `retryDelay` | number | 1000 | Delay between retries (ms) |
| `fallbackValue` | any | undefined | Value to return if all retries fail |
| `validator` | function | undefined | Function to validate parsed JSON |

## Error Types

- `JSONParseError` - Thrown when JSON parsing fails
- Validation errors - Thrown by custom validator functions
- Network errors - Passed through from LLM client

This robust approach ensures your LLM pipeline remains stable and provides meaningful responses even when individual LLM calls return malformed JSON.
