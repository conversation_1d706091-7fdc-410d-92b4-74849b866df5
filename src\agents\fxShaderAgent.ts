// src/agents/fxShaderAgent.ts
import { ArtBrief, Card, AnimationSpec, FxShaderPackage } from "../pipeline/types";
import { callLLM } from "../utils/llmClient";
import { parseWithRetry } from "../utils/jsonParser";
import { readFileSync } from "fs";
import path from "path";

const systemPrompt = readFileSync(
  path.join(__dirname, "../../prompts/fxShaderAgent.md"),
  "utf-8"
);

/**
 * Validates that the FX shader package has the expected structure
 */
function validateFxShaderPackage(data: any): FxShaderPackage {
  if (!data || typeof data !== 'object') {
    throw new Error('FX shader package is not an object');
  }

  if (!Array.isArray(data.fx_overlays)) {
    throw new Error('fx_overlays must be an array');
  }

  // Validate that all fx_overlays items are strings
  if (!data.fx_overlays.every((item: any) => typeof item === 'string')) {
    throw new Error('all fx_overlays items must be strings');
  }

  if (!data.shader_code || typeof data.shader_code !== 'string') {
    throw new Error('shader_code must be a non-empty string');
  }

  return data as FxShaderPackage;
}

export async function runFxShader(
  brief: ArtBrief,
  card: Card,
  animSpec: AnimationSpec
): Promise<FxShaderPackage> {
  const userPrompt = `Card: ${card.name}\nBrief: ${JSON.stringify(
    brief
  )}\nAnimation: ${JSON.stringify(animSpec)}`;

  const fallbackPackage: FxShaderPackage = {
    fx_overlays: ["glow", "sparkle"],
    shader_code: `// Basic sprite shader
uniform float time;
uniform sampler2D texture;
varying vec2 vUv;

void main() {
  vec4 color = texture2D(texture, vUv);
  float glow = sin(time * 2.0) * 0.1 + 0.9;
  gl_FragColor = color * glow;
}`
  };

  return parseWithRetry<FxShaderPackage>(
    () => callLLM(systemPrompt, userPrompt),
    {
      maxRetries: 3,
      retryDelay: 1000,
      fallbackValue: fallbackPackage,
      validator: validateFxShaderPackage
    }
  );
}