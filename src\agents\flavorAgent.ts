import { PipelineContext, Card } from "../pipeline/types";
import { callLLM } from "../utils/llmClient";
import { parseWithRetry } from "../utils/jsonParser";
import { readFileSync } from "fs";
import path from "path";

const systemPrompt = readFileSync(
  path.join(__dirname, "../../prompts/flavorAgent.md"),
  "utf-8"
);

export async function runFlavor(ctx: PipelineContext): Promise<Partial<Card>> {
  const userPrompt = `Card so far: ${JSON.stringify(ctx.mechanics, null, 2)}`;

  const fallbackFlavor: Partial<Card> = {
    background_lore: "A tale of collaboration between unlikely allies.",
    flavor_text: "Together, they are stronger than the sum of their parts.",
    concept_image_prompt: "Two characters working together in harmony"
  };

  return parseWithRetry<Partial<Card>>(
    () => callLLM(systemPrompt, userPrompt),
    {
      maxRetries: 3,
      retryDelay: 1000,
      fallbackValue: fallbackFlavor
    }
  );
}